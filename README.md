# 绿盟自动登录脚本

基于CSV文件中的登录流程实现的绿盟系统自动登录脚本，支持验证码自动识别。

## 功能特性

- ✅ 自动获取验证码图片和identifier
- ✅ 使用ddddocr进行验证码自动识别
- ✅ 自动执行登录流程
- ✅ 支持重试机制
- ✅ 详细的日志输出
- ✅ 模块化设计，易于扩展

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install requests ddddocr
```

## 使用方法

### 1. 基本使用

```python
from auto_login import GreenLeagueLogin

# 创建登录客户端
client = GreenLeagueLogin(host="************")

# 自动登录
success, result = client.auto_login()

if success:
    print("登录成功！")
    token = result.get('data', {}).get('token')
    print(f"Token: {token}")
else:
    print("登录失败！")
```

### 2. 自定义参数

```python
# 自定义主机地址
client = GreenLeagueLogin(host="your-host-ip")

# 自定义登录参数
success, result = client.auto_login(
    username="your-username",
    password="your-encrypted-password",
    max_retries=5
)
```

### 3. 分步骤执行

```python
client = GreenLeagueLogin()

# 步骤1: 获取验证码
image_data, identifier = client.get_captcha()

# 步骤2: 识别验证码
captcha_code = client.recognize_captcha(image_data)

# 步骤3: 执行登录
success, result = client.login(username, password, captcha_code, identifier)
```

## 登录流程说明

根据CSV文件分析，登录流程包含以下步骤：

### 1. 获取验证码
- **请求**: `GET /interface/myauth/captcha/`
- **响应**: 包含base64编码的验证码图片和identifier

### 2. 登录验证
- **请求**: `POST /interface/myauth/login`
- **参数**:
  - `username`: 用户名
  - `password`: 加密后的密码
  - `captcha_code`: 验证码识别结果
  - `identifier`: 验证码标识符

## 重要参数说明

### 登录参数
- `username`: 默认为 "admin"
- `password`: 加密后的密码，默认为 "U2FsdGVkX18pUgexLHc5Y8RrbtAlXz+3EZrDpYJqFqE="
- `captcha_code`: 由ddddocr自动识别
- `identifier`: 从验证码接口自动获取

### 请求头
脚本会自动设置以下重要的请求头：
- `User-Agent`: 模拟Chrome浏览器
- `Content-Type`: application/json;charset=UTF-8
- `Referer`: 设置正确的来源页面
- `Origin`: 设置正确的源地址

## 运行示例

```bash
# 运行基本示例
python example_usage.py

# 或直接运行主脚本
python auto_login.py
```

## 输出示例

```
==================================================
开始绿盟自动登录流程
==================================================

第 1 次尝试登录...
1. 获取验证码...
✓ 成功获取验证码，identifier: kz67lyv7s7olpi0xmjy0qcozuk4obwl3
2. 识别验证码...
✓ 验证码识别结果: fxne
3. 执行登录...
✓ 登录成功: 用户登录成功
  用户名: admin
  用户组: ADMINISTRATOR
  Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

==================================================
登录成功！
==================================================
```

## 注意事项

1. **密码加密**: 脚本中使用的密码是已经加密的，如需使用其他密码，请确保使用相同的加密方式
2. **网络环境**: 确保能够访问目标主机的HTTP服务
3. **验证码识别**: ddddocr对简单验证码识别率较高，复杂验证码可能需要调整
4. **重试机制**: 默认最多重试3次，可根据需要调整

## 文件结构

```
.
├── auto_login.py          # 主要的登录脚本
├── example_usage.py       # 使用示例
├── requirements.txt       # 依赖包列表
├── README.md             # 说明文档
└── 绿盟登录.csv          # 原始登录流程数据
```

## 扩展功能

可以基于此脚本扩展以下功能：
- 添加配置文件支持
- 实现登录状态保持
- 添加更多的错误处理
- 支持其他验证码识别方法
- 添加日志文件输出
