#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绿盟自动登录脚本
根据CSV文件中的登录流程实现自动化登录
"""

import requests
import json
import base64
import ddddocr
from io import BytesIO
import time

class GreenLeagueLogin:
    def __init__(self, host="************"):
        self.host = host
        self.base_url = f"http://{host}"
        self.session = requests.Session()
        
        # 设置请求头，模拟浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'sec-ch-ua': '"Not_A Brand";v="99", "Google Chrome";v="109", "Chromium";v="109"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'https://{host}/'
        })
        
        # 初始化验证码识别器
        self.ocr = ddddocr.DdddOcr()
    
    def get_captcha(self):
        """
        获取验证码图片和identifier
        返回: (captcha_image_data, identifier)
        """
        try:
            url = f"{self.base_url}/interface/myauth/captcha/"
            response = self.session.get(url)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    # 获取base64编码的图片数据和identifier
                    image_base64 = data['data']['mg_str']['image']
                    identifier = data['data']['identifier']
                    
                    # 解码base64图片
                    image_data = base64.b64decode(image_base64)
                    
                    print(f"✓ 成功获取验证码，identifier: {identifier}")
                    return image_data, identifier
                else:
                    print(f"✗ 获取验证码失败: {data.get('message', '未知错误')}")
                    return None, None
            else:
                print(f"✗ 请求验证码失败，状态码: {response.status_code}")
                return None, None
                
        except Exception as e:
            print(f"✗ 获取验证码异常: {str(e)}")
            return None, None
    
    def recognize_captcha(self, image_data):
        """
        识别验证码
        参数: image_data - 图片二进制数据
        返回: 识别结果字符串
        """
        try:
            result = self.ocr.classification(image_data)
            print(f"✓ 验证码识别结果: {result}")
            return result
        except Exception as e:
            print(f"✗ 验证码识别失败: {str(e)}")
            return None
    
    def login(self, username, password, captcha_code, identifier):
        """
        执行登录
        参数:
            username: 用户名
            password: 密码（已加密）
            captcha_code: 验证码
            identifier: 验证码标识符
        """
        try:
            url = f"{self.base_url}/interface/myauth/login"
            
            # 构造登录数据
            login_data = {
                "username": username,
                "password": password,
                "captcha_code": captcha_code,
                "identifier": identifier
            }
            
            # 设置Content-Type
            headers = {
                'Content-Type': 'application/json;charset=UTF-8',
                'Origin': f'https://{self.host}'
            }
            
            response = self.session.post(url, json=login_data, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    print(f"✓ 登录成功: {data.get('message', '登录成功')}")
                    
                    # 提取重要信息
                    user_info = data.get('data', {})
                    token = user_info.get('token')
                    username = user_info.get('user_info', {}).get('username')
                    group_name = user_info.get('user_info', {}).get('group_name')
                    
                    print(f"  用户名: {username}")
                    print(f"  用户组: {group_name}")
                    print(f"  Token: {token[:50]}..." if token else "  Token: 未获取到")
                    
                    return True, data
                else:
                    print(f"✗ 登录失败: {data.get('message', '未知错误')}")
                    return False, data
            else:
                print(f"✗ 登录请求失败，状态码: {response.status_code}")
                return False, None
                
        except Exception as e:
            print(f"✗ 登录异常: {str(e)}")
            return False, None
    
    def auto_login(self, username="admin", password="U2FsdGVkX18pUgexLHc5Y8RrbtAlXz+3EZrDpYJqFqE=", max_retries=3):
        """
        自动登录流程
        参数:
            username: 用户名，默认为admin
            password: 加密后的密码
            max_retries: 最大重试次数
        """
        print("=" * 50)
        print("开始绿盟自动登录流程")
        print("=" * 50)
        
        for attempt in range(max_retries):
            print(f"\n第 {attempt + 1} 次尝试登录...")
            
            # 步骤1: 获取验证码
            print("1. 获取验证码...")
            image_data, identifier = self.get_captcha()
            
            if not image_data or not identifier:
                print("获取验证码失败，重试...")
                time.sleep(1)
                continue
            
            # 步骤2: 识别验证码
            print("2. 识别验证码...")
            captcha_code = self.recognize_captcha(image_data)
            
            if not captcha_code:
                print("验证码识别失败，重试...")
                time.sleep(1)
                continue
            
            # 步骤3: 执行登录
            print("3. 执行登录...")
            success, result = self.login(username, password, captcha_code, identifier)
            
            if success:
                print("\n" + "=" * 50)
                print("登录成功！")
                print("=" * 50)
                return True, result
            else:
                print(f"登录失败，{max_retries - attempt - 1} 次重试机会剩余")
                time.sleep(2)
        
        print("\n" + "=" * 50)
        print("登录失败，已达到最大重试次数")
        print("=" * 50)
        return False, None

def main():
    """主函数"""
    # 创建登录实例
    login_client = GreenLeagueLogin()
    
    # 执行自动登录
    success, result = login_client.auto_login()
    
    if success:
        print("\n自动登录流程完成！")
    else:
        print("\n自动登录流程失败！")

if __name__ == "__main__":
    main()
