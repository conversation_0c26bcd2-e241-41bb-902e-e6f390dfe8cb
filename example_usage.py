#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绿盟自动登录使用示例
"""

from auto_login import GreenLeagueLogin

def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建登录客户端
    client = GreenLeagueLogin(host="************")
    
    # 使用默认参数自动登录
    success, result = client.auto_login()
    
    if success:
        print("登录成功！")
        # 可以在这里添加登录后的操作
    else:
        print("登录失败！")

def example_custom_credentials():
    """自定义凭据示例"""
    print("=== 自定义凭据示例 ===")
    
    # 创建登录客户端
    client = GreenLeagueLogin(host="************")
    
    # 自定义用户名和密码
    username = "admin"
    password = "U2FsdGVkX18pUgexLHc5Y8RrbtAlXz+3EZrDpYJqFqE="  # 这是加密后的密码
    
    # 执行登录
    success, result = client.auto_login(username=username, password=password, max_retries=5)
    
    if success:
        print("使用自定义凭据登录成功！")
        # 提取token等信息
        token = result.get('data', {}).get('token')
        print(f"获取到的Token: {token}")
    else:
        print("使用自定义凭据登录失败！")

def example_step_by_step():
    """分步骤执行示例"""
    print("=== 分步骤执行示例 ===")
    
    client = GreenLeagueLogin(host="************")
    
    # 步骤1: 获取验证码
    print("步骤1: 获取验证码")
    image_data, identifier = client.get_captcha()
    
    if image_data and identifier:
        print(f"验证码获取成功，identifier: {identifier}")
        
        # 步骤2: 识别验证码
        print("步骤2: 识别验证码")
        captcha_code = client.recognize_captcha(image_data)
        
        if captcha_code:
            print(f"验证码识别成功: {captcha_code}")
            
            # 步骤3: 执行登录
            print("步骤3: 执行登录")
            username = "admin"
            password = "U2FsdGVkX18pUgexLHc5Y8RrbtAlXz+3EZrDpYJqFqE="
            
            success, result = client.login(username, password, captcha_code, identifier)
            
            if success:
                print("分步骤登录成功！")
            else:
                print("分步骤登录失败！")
        else:
            print("验证码识别失败！")
    else:
        print("验证码获取失败！")

if __name__ == "__main__":
    # 运行基本使用示例
    example_basic_usage()
    
    print("\n" + "="*60 + "\n")
    
    # 运行自定义凭据示例
    # example_custom_credentials()
    
    # 运行分步骤示例
    # example_step_by_step()
